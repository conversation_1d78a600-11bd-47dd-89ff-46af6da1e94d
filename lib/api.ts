import axios, { AxiosResponse, AxiosError } from 'axios';
import {
  ENSRootRegistrationRequest,
  ENSRootRegistrationResponse,
  SubnameAvailabilityResponse,
  SubnameClaimRequest,
  SubnameClaimResponse
} from './types/ens';

const API_BASE_URL = 'https://api.crefy-connect-v2.crefy.xyz/api/v1';

// Configure axios defaults
axios.defaults.timeout = 30000; // 30 second timeout
axios.defaults.headers.common['Accept'] = 'application/json';
axios.defaults.headers.common['Content-Type'] = 'application/json';

export interface ApiResponse<T = unknown> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
}

export interface User {
  id: string;
  email: string;
  isVerified: boolean;
  createdAt?: string;
  updatedAt?: string;
}

// Utility functions for validation
export const validateUserId = (userId: string): boolean => {
  // User ID should be a non-empty string with valid format
  if (!userId || typeof userId !== 'string') {
    return false;
  }
  
  // Check for common valid formats (UUID, MongoDB ObjectId, or custom format)
  const validFormats = [
    /^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$/, // UUID
    /^[0-9a-fA-F]{24}$/, // MongoDB ObjectId
    /^(dev_|user_)[a-zA-Z0-9_-]+$/, // Custom format with prefix
  ];
  
  return validFormats.some(format => format.test(userId));
};

export const validateEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

export interface AuthResponse {
  token: string;
  user: User;
}

export interface RegisterRequest {
  name: string;
  email: string;
  password: string;
}

export interface VerifyRequest {
  email: string;
  otp: string;
}

export interface LoginRequest {
  email: string;
  password: string;
}

export interface Application {
  id: string;
  appId: string;
  name: string;
  description?: string;
  redirectUrls: string[];
  allowedDomains?: string[];
  iconUrl?: string;
  isActive?: boolean;
  createdAt: string;
  updatedAt?: string;
}

// Application with API key (for individual app responses)
export interface ApplicationWithApiKey extends Application {
  apiKey?: string;
}

// Application without API key (for list responses)
export interface ApplicationListItem extends Application {
  apiKey?: never;
}

// New API response format for applications list
export interface ApplicationsListResponse {
  success: boolean;
  count: number;
  data: ApplicationListItem[];
}

export interface CreateApplicationRequest {
  name: string;
  description?: string;
  redirectUrls: string[];
  iconUrl?: string;
}

export interface UpdateApplicationRequest {
  name?: string;
  description?: string;
  redirectUrls?: string[];
  iconUrl?: string;
}

export interface VerifyAppRequest {
  appId: string;
  apiKey: string;
}

export interface VerifyAppResponse {
  isValid: boolean;
  appId: string;
  developerId: string;
  allowedDomains: string[];
}

// Delete application response
export interface DeleteApplicationResponse {
  success: boolean;
  message: string;
}

// Social Wallet Authentication interfaces


// Developer management interfaces (updated for Swagger spec)
export interface DeveloperRegisterRequest {
  name: string;
  email: string;
  password: string;
}

export interface DeveloperRegisterResponse {
  id: string;
  name: string;
  email: string;
  isActive: boolean;
  createdAt: string;
}

export interface DeveloperLoginRequest {
  email: string;
  password: string;
}

export interface DeveloperLoginResponse {
  message: string;
  token: string;
  data: {
    id: string;
    name: string;
    email: string;
    isActive?: boolean;
    createdAt: string;
  };
}

export interface DeveloperProfileResponse {
  id: string;
  name: string;
  email: string;
  isActive: boolean;
  createdAt: string;
}

export interface ResendOTPRequest {
  email: string;
}

export interface ResendOTPResponse {
  success: boolean;
  message: string;
}

// Password reset interfaces
export interface ForgotPasswordRequest {
  email: string;
}

export interface ForgotPasswordResponse {
  success: boolean;
  message: string;
}

export interface VerifyResetTokenRequest {
  token: string;
}

export interface VerifyResetTokenResponse {
  success: boolean;
  message: string;
}

export interface ResetPasswordRequest {
  token: string;
  newPassword: string;
}

export interface ResetPasswordResponse {
  success: boolean;
  message: string;
}

// ENS Transfer Types
export interface PrepareRegistrarRequest {
  ensName: string;
  chain: string;
  from: string;
}

export interface PrepareRegistrarResponse {
  success: boolean;
  message: string;
  data: {
    to: string;
    data: string;
    value: string;
    gas: string;
    from: string;
    chainId: number;
  };
}

export interface PrepareNameWrapperTransferRequest {
  chain: string;
  from: string;
  to: string;
  id: string;
  amount: string;
  data: string;
}

export interface PrepareNameWrapperTransferResponse {
  success: boolean;
  message: string;
  data: {
    to: string;
    data: string;
    value: string;
    gas: string;
    chainId: number;
  };
}

class ApiService {
  private async makeRequest<T>(
    endpoint: string,
    method: 'GET' | 'POST' | 'PUT' | 'DELETE' = 'GET',
    data?: unknown,
    headers?: Record<string, string>
  ): Promise<ApiResponse<T>> {
    try {
      const url = `${API_BASE_URL}${endpoint}`;

      const config = {
        method,
        url,
        data,
        headers: {
          'ngrok-skip-browser-warning': 'true', // Skip ngrok browser warning
          ...headers,
        },
      };



      const response: AxiosResponse<any> = await axios(config);



      // Handle responses from external API
      // External API returns responses with a 'success' field that we need to check
      const responseData = response.data;

      // Check if the external API response indicates success
      if (responseData && typeof responseData === 'object' && 'success' in responseData) {
        if (responseData.success === false) {
          // External API returned an error response with HTTP 200
          return {
            success: false,
            error: responseData.message || responseData.error || 'API request failed',
          };
        }

        // External API returned success, return the full response (not just the data field)
        // This preserves the token, message, and data fields that the frontend expects
        return {
          success: true,
          data: responseData, // Return the full external API response
        };
      }

      // Fallback for responses without success field (assume success if HTTP 200)
      return {
        success: true,
        data: responseData,
      };

    } catch (error) {
      if (axios.isAxiosError(error)) {
        const axiosError = error as AxiosError;

        // Handle HTTP error responses from external API
        if (axiosError.response) {
          const status = axiosError.response.status;
          const responseData = axiosError.response.data as Record<string, unknown>;

          // Handle different error response formats from external API
          let errorMessage = `HTTP ${status}: ${axiosError.message}`;

          if (responseData) {
            if (typeof responseData === 'string') {
              errorMessage = responseData;
            } else if (responseData.error) {
              errorMessage = String(responseData.error);
            } else if (responseData.message) {
              errorMessage = String(responseData.message);
            } else if (responseData.detail) {
              errorMessage = String(responseData.detail);
            }
          }

          // Add specific handling for authentication errors with more details
          if (status === 401) {
            console.error('🚨 401 Unauthorized Error Details:', {
              status,
              responseData,
              originalMessage: errorMessage,
              url: axiosError.config?.url,
              method: axiosError.config?.method,
              headers: axiosError.config?.headers,
              requestData: axiosError.config?.data
            });

            // Log the full response for debugging
            console.error('🔍 Full API Response:', axiosError.response);

            if (errorMessage.toLowerCase().includes('token') || errorMessage.toLowerCase().includes('unauthorized')) {
              errorMessage = 'Invalid token payload';
            }
          }

          return {
            success: false,
            error: errorMessage,
          };
        }

        // Handle network errors
        if (axiosError.request) {
          return {
            success: false,
            error: 'Network error. The backend service may be down or unreachable.',
          };
        }
      }

      // Handle other errors
      return {
        success: false,
        error: error instanceof Error ? error.message : 'An unexpected error occurred',
      };
    }
  }

  // Developer Management endpoints (updated for Swagger spec)
  async register(data: DeveloperRegisterRequest): Promise<ApiResponse<DeveloperRegisterResponse>> {
    // Validate email format
    if (!validateEmail(data.email)) {
      return {
        success: false,
        error: 'Invalid email format',
      };
    }

    if (!data.password || data.password.length < 6) {
      return {
        success: false,
        error: 'Password must be at least 6 characters long',
      };
    }

    if (!data.name || data.name.trim().length === 0) {
      return {
        success: false,
        error: 'Name is required',
      };
    }

    return this.makeRequest('/developers/register', 'POST', data);
  }

  async verify(data: VerifyRequest): Promise<ApiResponse<{ success: boolean; message: string }>> {
    if (!validateEmail(data.email)) {
      return {
        success: false,
        error: 'Invalid email format',
      };
    }

    if (!data.otp || data.otp.length !== 6) {
      return {
        success: false,
        error: 'OTP must be 6 digits',
      };
    }

    return this.makeRequest('/developers/verify-otp', 'POST', data);
  }

  async login(data: DeveloperLoginRequest): Promise<ApiResponse<DeveloperLoginResponse>> {
    if (!validateEmail(data.email)) {
      return {
        success: false,
        error: 'Invalid email format',
      };
    }

    if (!data.password) {
      return {
        success: false,
        error: 'Password is required',
      };
    }


    return this.makeRequest('/developers/login', 'POST', data);
  }

  async getProfile(token: string): Promise<ApiResponse<DeveloperProfileResponse>> {
    if (!token || typeof token !== 'string') {
      return {
        success: false,
        error: 'Invalid or missing authentication token',
      };
    }

    return this.makeRequest('/developers/profile', 'GET', undefined, {
      Authorization: `Bearer ${token}`,
    });
  }

  async resendOTP(data: ResendOTPRequest): Promise<ApiResponse<ResendOTPResponse>> {
    if (!validateEmail(data.email)) {
      return {
        success: false,
        error: 'Invalid email format',
      };
    }

    return this.makeRequest('/developers/resend-otp', 'POST', data);
  }

  async deleteDeveloper(token: string): Promise<ApiResponse<{ success: boolean; message: string }>> {
    if (!token || typeof token !== 'string') {
      return {
        success: false,
        error: 'Invalid or missing authentication token',
      };
    }

    return this.makeRequest('/developers', 'DELETE', undefined, {
      Authorization: `Bearer ${token}`,
    });
  }

  // Password reset endpoints
  async forgotPassword(data: ForgotPasswordRequest): Promise<ApiResponse<ForgotPasswordResponse>> {
    if (!validateEmail(data.email)) {
      return {
        success: false,
        error: 'Invalid email format',
      };
    }

    return this.makeRequest('/developers/forgot-password', 'POST', data);
  }

  async verifyResetToken(token: string): Promise<ApiResponse<VerifyResetTokenResponse>> {
    if (!token || typeof token !== 'string' || token.trim().length === 0) {
      return {
        success: false,
        error: 'Token is required',
      };
    }

    return this.makeRequest(`/developers/verify-reset-token?token=${encodeURIComponent(token)}`, 'GET');
  }

  async resetPassword(data: ResetPasswordRequest): Promise<ApiResponse<ResetPasswordResponse>> {
    if (!data.token || typeof data.token !== 'string' || data.token.trim().length === 0) {
      return {
        success: false,
        error: 'Token is required',
      };
    }

    if (!data.newPassword || data.newPassword.length < 6) {
      return {
        success: false,
        error: 'Password must be at least 6 characters long',
      };
    }

    return this.makeRequest('/developers/reset-password', 'POST', data);
  }





  // Application management endpoints
  async createApplication(data: CreateApplicationRequest, token: string): Promise<ApiResponse<ApplicationWithApiKey>> {
    return this.makeRequest('/apps', 'POST', data, {
      Authorization: `Bearer ${token}`,
    });
  }

  async getApplications(token: string): Promise<ApiResponse<ApplicationListItem[]>> {
    try {
      const response = await this.makeRequest('/apps', 'GET', undefined, {
        Authorization: `Bearer ${token}`,
      });



      // Handle the new API response format
      if (response.success && response.data) {
        const responseData = response.data as any;
        // Check if response.data has the new format with success, count, and data fields
        if (responseData.success && Array.isArray(responseData.data)) {
          return {
            success: true,
            data: responseData.data,
            message: response.message
          };
        }
        // Check if response.data is directly an array (old format)
        else if (Array.isArray(responseData)) {
          return {
            success: true,
            data: responseData,
            message: response.message
          };
        }
        // Handle case where data exists but is not in expected format
        else {
          console.warn('Unexpected applications response format:', responseData);
          return {
            success: true,
            data: [],
            message: 'No applications found'
          };
        }
      }

      return {
        success: false,
        error: response.error || 'Failed to fetch applications'
      };
    } catch (error) {
      console.error('Applications fetch error:', error);
      return {
        success: false,
        error: 'Failed to fetch applications'
      };
    }
  }

  async getApplication(appId: string, token: string): Promise<ApiResponse<ApplicationWithApiKey>> {
    if (!appId || typeof appId !== 'string') {
      return {
        success: false,
        error: 'Invalid app ID',
      };
    }

    return this.makeRequest(`/apps/${appId}`, 'GET', undefined, {
      Authorization: `Bearer ${token}`,
    });
  }

  async verifyApplication(data: VerifyAppRequest): Promise<ApiResponse<VerifyAppResponse>> {
    return this.makeRequest('/apps/verify', 'POST', data);
  }

  async deleteApplication(appId: string, token: string): Promise<ApiResponse<DeleteApplicationResponse>> {
    return this.makeRequest(`/apps/${appId}`, 'DELETE', undefined, {
      Authorization: `Bearer ${token}`,
    });
  }

  async updateApplication(appId: string, data: UpdateApplicationRequest, token: string): Promise<ApiResponse<ApplicationWithApiKey>> {
    return this.makeRequest(`/apps/${appId}`, 'PUT', data, {
      Authorization: `Bearer ${token}`,
    });
  }

  // ENS Management endpoints
  async getENSRoots(token: string): Promise<ApiResponse<any[]>> {
    return this.makeRequest('/ens/roots', 'GET', undefined, {
      Authorization: `Bearer ${token}`,
    });
  }

  async registerENSRoot(data: ENSRootRegistrationRequest, token: string, appId: string, walletAuthToken: string): Promise<ApiResponse<ENSRootRegistrationResponse>> {
    // Validate ENS name format (supports .eth and other valid TLDs)
    const validTLDs = ['.eth', '.xyz', '.luxe', '.kred', '.art', '.club'];
    const hasValidTLD = validTLDs.some(tld => data.ens_name?.toLowerCase().endsWith(tld));

    if (!data.ens_name || !hasValidTLD) {
      return {
        success: false,
        error: 'ENS Name must end with a valid TLD (.eth, .xyz, .luxe, .kred, .art, .club)',
      };
    }

    if (!data.contractAddress || !data.chain) {
      return {
        success: false,
        error: 'Contract address and chain are required',
      };
    }

    if (!appId) {
      return {
        success: false,
        error: 'Application ID is required for ENS registration',
      };
    }

    if (!walletAuthToken) {
      return {
        success: false,
        error: 'Wallet authentication is required for ENS registration',
      };
    }

    const headers: Record<string, string> = {
      Authorization: `Bearer ${token}`,
      'x-api-key': appId,
      'x-wallet-auth': walletAuthToken,
    };

    return this.makeRequest('/ens/roots', 'POST', data, headers);
  }

  async checkSubnameAvailability(subName: string, chain: string, token: string, appId?: string): Promise<ApiResponse<SubnameAvailabilityResponse>> {
    if (!subName || !chain) {
      return {
        success: false,
        error: 'Subname and chain are required',
      };
    }

    const params = new URLSearchParams({
      subName: subName.toLowerCase(),
      chain: chain.toLowerCase(),
    });

    const headers: Record<string, string> = {
      Authorization: `Bearer ${token}`,
    };

    // Add appId as x-api-key header if provided
    if (appId) {
      headers['x-api-key'] = appId;
    }

    return this.makeRequest(`/ens/subnames/check-availability?${params.toString()}`, 'GET', undefined, headers);
  }

  async claimSubname(data: SubnameClaimRequest, token: string, appId?: string): Promise<ApiResponse<SubnameClaimResponse>> {
    if (!data.subName || !data.chain) {
      return {
        success: false,
        error: 'Subname and chain are required',
      };
    }

    // Validate subname format
    const subnameRegex = /^[a-z0-9-]+$/;
    if (!subnameRegex.test(data.subName)) {
      return {
        success: false,
        error: 'Invalid subname format. Only lowercase letters, numbers, and hyphens are allowed',
      };
    }

    const headers: Record<string, string> = {
      Authorization: `Bearer ${token}`,
    };

    // Add appId as x-api-key header if provided
    if (appId) {
      headers['x-api-key'] = appId;
    }

    return this.makeRequest('/ens/subnames/claim', 'POST', data, headers);
  }

  // ENS Transfer Functions
  async prepareRegistrarTransaction(data: PrepareRegistrarRequest, token: string, appId: string): Promise<ApiResponse<PrepareRegistrarResponse>> {
    if (!data.ensName || !data.chain) {
      return {
        success: false,
        error: 'ENS name and chain are required',
      };
    }

    if (!appId || typeof appId !== 'string' || appId.trim().length === 0) {
      return {
        success: false,
        error: 'Application ID is required for ENS registrar operations',
      };
    }

    // Validate ENS name format
    const ensRegex = /^[a-zA-Z0-9-]+\.eth$/;
    if (!ensRegex.test(data.ensName)) {
      return {
        success: false,
        error: 'Invalid ENS name format. Must end with .eth',
      };
    }

    const headers: Record<string, string> = {
      Authorization: `Bearer ${token}`,
      'x-api-key': appId,
    };

    try {
      const response = await this.makeRequest<PrepareRegistrarResponse>('/ens/prepare-registrar', 'POST', data, headers);

      // Validate that the backend returns correct contract address
      if (response.success && response.data?.data) {
        const transactionData = response.data.data;
        const expectedFactoryAddress = "******************************************";

        // Check if backend returned invalid transaction data (targeting user address instead of contract)
        if (transactionData.to?.toLowerCase() !== expectedFactoryAddress.toLowerCase()) {
          console.warn('Backend returned incorrect contract address:', {
            expected: expectedFactoryAddress,
            received: transactionData.to,
            correcting: true
          });

          // Correct the transaction data to target the proper Factory contract
          transactionData.to = expectedFactoryAddress;
        }
      }

      return response;
    } catch (error) {
      console.error('Backend API error:', error);
      return {
        success: false,
        error: 'Failed to prepare registrar transaction via backend API'
      };
    }
  }

  async prepareNameWrapperTransfer(data: PrepareNameWrapperTransferRequest, token: string, appId: string): Promise<ApiResponse<PrepareNameWrapperTransferResponse>> {
    if (!data.from || !data.to || !data.id || !data.chain) {
      return {
        success: false,
        error: 'From address, to address, token ID, and chain are required',
      };
    }

    if (!appId || typeof appId !== 'string' || appId.trim().length === 0) {
      return {
        success: false,
        error: 'Application ID is required for ENS NameWrapper operations',
      };
    }

    // Validate Ethereum addresses
    const addressRegex = /^0x[a-fA-F0-9]{40}$/;
    if (!addressRegex.test(data.from)) {
      return {
        success: false,
        error: 'Invalid from address format',
      };
    }

    if (!addressRegex.test(data.to)) {
      return {
        success: false,
        error: 'Invalid to address format',
      };
    }

    // Validate token ID
    if (!data.id || isNaN(Number(data.id))) {
      return {
        success: false,
        error: 'Invalid token ID',
      };
    }

    const headers: Record<string, string> = {
      Authorization: `Bearer ${token}`,
      'x-api-key': appId,
    };

    try {
      const response = await this.makeRequest<PrepareNameWrapperTransferResponse>('/ens/namewrapper/prepare-transfer', 'POST', data, headers);

      // Validate that the backend returns correct contract address
      if (response.success && response.data?.data) {
        const transactionData = response.data.data;

        // Import the helper function to get the correct address for the chain
        const { getNameWrapperAddress } = await import('./contracts/name-wrapper');
        const expectedNameWrapperAddress = getNameWrapperAddress(data.chain);

        // Check if backend returned invalid transaction data (targeting user address instead of contract)
        if (transactionData.to?.toLowerCase() !== expectedNameWrapperAddress.toLowerCase()) {
          console.warn('Backend returned incorrect NameWrapper contract address:', {
            chain: data.chain,
            expected: expectedNameWrapperAddress,
            received: transactionData.to,
            correcting: true
          });

          // Correct the transaction data to target the proper NameWrapper contract
          transactionData.to = expectedNameWrapperAddress;
        }
      }

      return response;
    } catch (error) {
      console.error('Backend API error:', error);
      return {
        success: false,
        error: 'Failed to prepare NameWrapper transfer via backend API'
      };
    }
  }
}

export const apiService = new ApiService();