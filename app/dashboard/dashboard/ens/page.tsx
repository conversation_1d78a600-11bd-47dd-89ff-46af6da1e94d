'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { useToast } from "@/lib/toast-context";
import { useAuth } from "@/lib/auth-context";
import { apiService } from "@/lib/api";
import { ConnectButton } from '@rainbow-me/rainbowkit';
import { useAccount } from 'wagmi';
import {
  WalletIcon,
  CheckCircleIcon,
  GlobeIcon,
  ArrowUpDownIcon,
  SettingsIcon
} from "lucide-react";

import { DashboardLayoutWrapper } from "@/components/shared/dashboard/layout-wrapper";
import { ENSWorkflow } from "@/components/ens/ens-workflow";
import { ENSDashboard } from "@/components/ens/ens-dashboard";
import { ENSTransferFunctions } from "@/components/ens/ens-transfer-functions-simple";
import { ApplicationSelection } from "@/components/ens/application-selection";
import { ApplicationWithApiKey } from "@/lib/api";
import { ENSConnection } from "@/lib/types/ens";

export default function ENSIntegrationPage() {
  const { address, isConnected } = useAccount();
  const { showToast } = useToast();
  const { token } = useAuth();

  const [selectedApplication, setSelectedApplication] = useState<ApplicationWithApiKey | null>(null);
  const [ensConnections, setEnsConnections] = useState<ENSConnection[]>([]);
  const [activeTab, setActiveTab] = useState<'integration' | 'transfer' | 'functions'>('integration');
  const [existingENSData, setExistingENSData] = useState<{
    ensName: string;
    contractAddress: string;
    isActive: boolean;
  } | null>(null);

  // Load existing ENS data from API
  useEffect(() => {
    const loadENSData = async () => {
      if (!token || !selectedApplication) return;

      try {
        // Check if app already has an ENS name registered
        const response = await apiService.getENSName(token, selectedApplication.appId);
        
        if (response.success && response.data) {
          setExistingENSData(response.data);
          // Create a connection object for display
          const connection: ENSConnection = {
            id: Date.now().toString(),
            projectId: selectedApplication.appId,
            appId: selectedApplication.appId,
            ensName: response.data.ensName,
            owner: response.data.contractAddress,
            connectedAt: new Date().toISOString(),
            isActive: response.data.isActive,
            contractAddress: response.data.contractAddress,
            chain: 'sepolia'
          };
          setEnsConnections([connection]);
        } else {
          // No existing ENS registration
          setExistingENSData(null);
          setEnsConnections([]);
        }
      } catch (error) {
        console.error('Failed to load ENS data:', error);
        setExistingENSData(null);
        setEnsConnections([]);
      }
    };

    loadENSData();
  }, [selectedApplication, token]);

  const handleApplicationSelect = (_applicationId: string, application: ApplicationWithApiKey) => {
    setSelectedApplication(application);
    showToast({
      type: 'success',
      title: 'Application Selected',
      description: `Selected ${application.name} for ENS integration`
    });
  };

  const handleWorkflowSuccess = (_data: any) => {
    showToast({
      type: 'success',
      title: 'ENS Integration Complete! 🎉',
      description: 'Your ENS domain has been successfully integrated'
    });
    // Refresh the page data
    window.location.reload();
  };

  return (
    <DashboardLayoutWrapper title="ENS Integration">
      <div className="min-h-screen bg-gradient-to-br from-gray-50 via-white to-gray-100">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          {/* Header */}
          <div className="mb-8">
            <h1 className="text-3xl font-bold bg-gradient-to-r from-[#4A148C] to-[#7B1FA2] bg-clip-text text-transparent">
              ENS Integration
            </h1>
            <p className="text-gray-600 mt-2">
              Connect your ENS domains to enable decentralized identity for your applications
            </p>
          </div>

          {/* Tab Navigation */}
          <div className="mb-8">
            <div className="border-b border-gray-200">
              <nav className="-mb-px flex space-x-8">
                <button
                  onClick={() => setActiveTab('integration')}
                  className={`py-2 px-1 border-b-2 font-medium text-sm ${
                    activeTab === 'integration'
                      ? 'border-[#4A148C] text-[#4A148C]'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  <GlobeIcon className="h-4 w-4 inline mr-2" />
                  ENS Integration
                </button>
                <button
                  onClick={() => setActiveTab('transfer')}
                  className={`py-2 px-1 border-b-2 font-medium text-sm ${
                    activeTab === 'transfer'
                      ? 'border-[#4A148C] text-[#4A148C]'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  <ArrowUpDownIcon className="h-4 w-4 inline mr-2" />
                  Transfer Ownership
                </button>
                <button
                  onClick={() => setActiveTab('functions')}
                  className={`py-2 px-1 border-b-2 font-medium text-sm ${
                    activeTab === 'functions'
                      ? 'border-[#4A148C] text-[#4A148C]'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  <SettingsIcon className="h-4 w-4 inline mr-2" />
                  Advanced Functions
                </button>
              </nav>
            </div>
          </div>

          {/* Tab Content */}
          {activeTab === 'integration' && (
            <div className="max-w-4xl mx-auto">
              {!isConnected ? (
                <Card className="p-6 bg-gradient-to-br from-[#4A148C]/5 via-[#7B1FA2]/5 to-[#4A148C]/10 border border-[#B497D6]/30 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300">
                  <div className="text-center space-y-6">
                    <div className="w-16 h-16 mx-auto bg-gradient-to-r from-[#4A148C] to-[#7B1FA2] rounded-full flex items-center justify-center shadow-lg">
                      <WalletIcon className="h-8 w-8 text-white" />
                    </div>

                    <div className="space-y-3">
                      <h3 className="text-2xl font-bold bg-gradient-to-r from-[#4A148C] to-[#7B1FA2] bg-clip-text text-transparent">
                        Connect Your Wallet
                      </h3>
                      <p className="text-base text-gray-600 max-w-md mx-auto leading-relaxed">
                        To use ENS functionality, you need to connect your wallet. This allows us to verify
                        ENS ownership and register domains for your applications.
                      </p>
                    </div>

                    <div className="space-y-4">
                      <div className="flex justify-center">
                        <ConnectButton />
                      </div>

                      <div className="flex flex-col sm:flex-row items-center justify-center gap-4 text-sm text-gray-500">
                        <div className="flex items-center gap-2">
                          <CheckCircleIcon className="h-4 w-4 text-green-600" />
                          <span>Secure Connection</span>
                        </div>
                        <div className="flex items-center gap-2">
                          <CheckCircleIcon className="h-4 w-4 text-green-600" />
                          <span>ENS Compatible</span>
                        </div>
                      </div>
                    </div>

                    <div className="pt-4 border-t border-[#B497D6]/20">
                      <p className="text-xs text-gray-500">
                        Supported wallets: MetaMask, WalletConnect, Coinbase Wallet, and more
                      </p>
                    </div>
                  </div>
                </Card>
              ) : !selectedApplication ? (
                <ApplicationSelection
                  selectedApplicationId={selectedApplication?.appId}
                  onApplicationSelect={handleApplicationSelect}
                  onError={(error) => {
                    showToast({
                      type: 'error',
                      title: 'Application Error',
                      description: error
                    });
                  }}
                />
              ) : existingENSData ? (
                <ENSDashboard
                  selectedApplication={selectedApplication}
                  ensConnections={ensConnections}
                  onRefresh={() => {
                    window.location.reload();
                  }}
                />
              ) : (
                <ENSWorkflow
                  selectedApplication={selectedApplication}
                  onSuccess={handleWorkflowSuccess}
                />
              )}
            </div>
          )}

          {activeTab === 'transfer' && (
            <div className="max-w-4xl mx-auto">
              <Card className="bg-white/80 backdrop-blur-sm border border-[#B497D6]/20 shadow-lg rounded-2xl">
                <CardHeader>
                  <CardTitle className="text-lg font-bold bg-gradient-to-r from-[#4A148C] to-[#7B1FA2] bg-clip-text text-transparent">
                    Transfer ENS Ownership
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-gray-600 mb-4">
                    Transfer ownership of your ENS domains to another address.
                  </p>
                  {existingENSData ? (
                    <p className="text-sm text-gray-500">
                      Current ENS: {existingENSData.ensName}
                    </p>
                  ) : (
                    <p className="text-sm text-gray-500">
                      No ENS domain registered for this application yet.
                    </p>
                  )}
                </CardContent>
              </Card>
            </div>
          )}

          {activeTab === 'functions' && (
            <div className="max-w-4xl mx-auto">
              <ENSTransferFunctions
                ensName={existingENSData?.ensName || ''}
                currentOwner={address || ''}
                appId={selectedApplication?.appId}
                onSuccess={(_, transferType) => {
                  showToast({
                    type: 'success',
                    title: 'Transfer Function Complete',
                    description: `${transferType} completed successfully`
                  });
                }}
                onError={(error) => {
                  showToast({
                    type: 'error',
                    title: 'Transfer Failed',
                    description: error
                  });
                }}
              />
            </div>
          )}
        </div>
      </div>
    </DashboardLayoutWrapper>
  );
}
