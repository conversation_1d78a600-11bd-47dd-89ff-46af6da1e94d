'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectOption } from "@/components/ui/select";
import { ConnectButton } from '@rainbow-me/rainbowkit';
import { useAccount, useDisconnect, useWriteContract, useWaitForTransactionReceipt } from 'wagmi';
import { useToast } from "@/lib/toast-context";
import { useAuth } from "@/lib/auth-context";
import { apiService, ApplicationWithApiKey } from "@/lib/api";
import { getENSTokenId, isValidChain, namehash, validateNameWrapperTransfer } from "@/lib/ens-utils";
import { FACTORY_CONTRACT_ABI } from "@/lib/contracts/factory-contract";
import { getName<PERSON>rapper<PERSON>ddress, NAMEWRAPPER_CONTRACT_ABI } from "@/lib/contracts/name-wrapper";
import { ApplicationSelection } from "@/components/ens/application-selection";
import { ethers } from 'ethers';
import {
  WalletIcon,
  ArrowUpDownIcon,
  SettingsIcon,
  ArrowRightIcon,
  AlertCircleIcon,
  CheckCircleIcon,
  ExternalLinkIcon,
  UserIcon,
  ShieldCheckIcon,
  GlobeIcon,
  LogOutIcon,
  RefreshCwIcon
} from 'lucide-react';

export interface ENSTransferFunctionsProps {
  ensName?: string;
  currentOwner?: string;
  appId?: string;
  onSuccess?: (txHash: string, transferType: string) => void;
  onError?: (error: string) => void;
  className?: string;
}

type TransferType = 'registrar' | 'namewrapper';
type TransferStatus = 'idle' | 'preparing' | 'confirming' | 'pending' | 'success' | 'error';
type WorkflowStep = 'select-app' | 'select-ens' | 'transfer';

export function ENSTransferFunctions({
  ensName: initialEnsName,
  currentOwner: initialCurrentOwner,
  appId: initialAppId,
  onSuccess,
  onError,
  className = ""
}: ENSTransferFunctionsProps) {
  const { address, isConnected } = useAccount();
  const { disconnect } = useDisconnect();
  const { showToast } = useToast();
  const { token } = useAuth();

  // Wagmi contract write hooks
  const {
    writeContract: writeFactoryContract,
    data: factoryTxHash,
    isPending: isFactoryPending,
    error: factoryError
  } = useWriteContract();

  const {
    writeContract: writeNameWrapperContract,
    data: nameWrapperTxHash,
    isPending: isNameWrapperPending,
    error: nameWrapperError
  } = useWriteContract();

  // Transaction receipt hooks
  const {
    data: factoryReceipt,
    isLoading: isFactoryReceiptLoading,
    isSuccess: isFactorySuccess
  } = useWaitForTransactionReceipt({
    hash: factoryTxHash,
  });

  const {
    data: nameWrapperReceipt,
    isLoading: isNameWrapperReceiptLoading,
    isSuccess: isNameWrapperSuccess
  } = useWaitForTransactionReceipt({
    hash: nameWrapperTxHash,
  });

  // Application selection state
  const [selectedApplication, setSelectedApplication] = useState<ApplicationWithApiKey | null>(null);
  const [currentStep, setCurrentStep] = useState<WorkflowStep>('select-app');

  // State management
  const [selectedENSName, setSelectedENSName] = useState(initialEnsName || '');
  const [ensOwner, setEnsOwner] = useState(initialCurrentOwner || '');
  const [transferType, setTransferType] = useState<TransferType>('registrar');
  const [selectedChain, setSelectedChain] = useState<string>('sepolia');
  const [newOwnerAddress, setNewOwnerAddress] = useState('');
  const [transferStatus, setTransferStatus] = useState<TransferStatus>('idle');
  const [transferError, setTransferError] = useState<string | null>(null);
  const [txHash, setTxHash] = useState<string | null>(null);
  const [addressError, setAddressError] = useState<string | null>(null);
  const [isMounted, setIsMounted] = useState(false);

  // Create stable showToast reference
  const stableShowToast = useCallback((toast: Parameters<typeof showToast>[0]) => {
    showToast(toast);
  }, [showToast]);

  // Ensure component only renders on client side
  useEffect(() => {
    setIsMounted(true);
  }, []);

  // Handle successful factory contract transaction
  useEffect(() => {
    if (isFactorySuccess && factoryReceipt && factoryTxHash) {
      setTransferStatus('success');
      setTxHash(factoryTxHash);
      onSuccess?.(factoryTxHash, 'registrar');
    }
  }, [isFactorySuccess, factoryReceipt, factoryTxHash, onSuccess]);

  // Handle successful NameWrapper transaction
  useEffect(() => {
    if (isNameWrapperSuccess && nameWrapperReceipt && nameWrapperTxHash) {
      setTransferStatus('success');
      setTxHash(nameWrapperTxHash);
      onSuccess?.(nameWrapperTxHash, 'namewrapper');

      // Reset form after successful transfer
      setNewOwnerAddress('');
      setAddressError(null);
    }
  }, [isNameWrapperSuccess, nameWrapperReceipt, nameWrapperTxHash, onSuccess]);

  // Show success toasts separately to avoid dependency issues
  useEffect(() => {
    if (isFactorySuccess && factoryReceipt && factoryTxHash && selectedENSName) {
      stableShowToast({
        type: 'success',
        title: 'Registrar Created Successfully',
        description: `Subname registrar for ${selectedENSName} has been created successfully`
      });
    }
  }, [isFactorySuccess, factoryReceipt, factoryTxHash, selectedENSName, stableShowToast]);

  useEffect(() => {
    if (isNameWrapperSuccess && nameWrapperReceipt && nameWrapperTxHash && selectedENSName && newOwnerAddress) {
      stableShowToast({
        type: 'success',
        title: 'NameWrapper Transfer Complete',
        description: `${selectedENSName} has been successfully transferred to ${newOwnerAddress.slice(0, 6)}...${newOwnerAddress.slice(-4)}`
      });
    }
  }, [isNameWrapperSuccess, nameWrapperReceipt, nameWrapperTxHash, selectedENSName, newOwnerAddress, stableShowToast]);

  // Handle contract errors
  useEffect(() => {
    if (factoryError) {
      console.error('Factory contract error:', factoryError);
      setTransferStatus('error');
      const errorMessage = factoryError.message || 'Failed to create registrar';
      setTransferError(errorMessage);
      onError?.(errorMessage);
      stableShowToast({
        type: 'error',
        title: 'Registrar Creation Failed',
        description: errorMessage
      });
    }
  }, [factoryError, onError, stableShowToast]);

  useEffect(() => {
    if (nameWrapperError) {
      console.error('NameWrapper contract error:', nameWrapperError);
      setTransferStatus('error');
      const errorMessage = nameWrapperError.message || 'Failed to transfer via NameWrapper';
      setTransferError(errorMessage);
      onError?.(errorMessage);
      stableShowToast({
        type: 'error',
        title: 'NameWrapper Transfer Failed',
        description: errorMessage
      });
    }
  }, [nameWrapperError, onError, stableShowToast]);

  // Update transfer status based on pending states
  // Track Factory transaction states
  useEffect(() => {
    if (isFactoryPending) {
      setTransferStatus('confirming');
    } else if (factoryTxHash && isFactoryReceiptLoading) {
      setTransferStatus('pending');
      setTxHash(factoryTxHash);
      stableShowToast({
        type: 'info',
        title: 'Transaction Submitted',
        description: 'Registrar creation is being processed on the blockchain'
      });
    }
  }, [isFactoryPending, factoryTxHash, isFactoryReceiptLoading, stableShowToast]);

  // Track NameWrapper transaction states
  useEffect(() => {
    if (isNameWrapperPending) {
      setTransferStatus('confirming');
    } else if (nameWrapperTxHash && isNameWrapperReceiptLoading) {
      setTransferStatus('pending');
      setTxHash(nameWrapperTxHash);
      stableShowToast({
        type: 'info',
        title: 'Transaction Submitted',
        description: 'NameWrapper transfer is being processed on the blockchain'
      });
    }
  }, [isNameWrapperPending, nameWrapperTxHash, isNameWrapperReceiptLoading, stableShowToast]);

  // Initialize with provided appId if available
  useEffect(() => {
    if (initialAppId && !selectedApplication) {
      // If appId is provided, we can skip application selection
      // This maintains backward compatibility
      setCurrentStep('select-ens');
    } else if (isConnected && !initialAppId) {
      // If wallet is connected but no appId provided, start with app selection
      setCurrentStep('select-app');
    }
  }, [initialAppId, selectedApplication, isConnected]);

  // Handle application selection
  const handleApplicationSelect = (_applicationId: string, application: ApplicationWithApiKey) => {
    setSelectedApplication(application);
    setCurrentStep('select-ens');
    setTransferError(null);

    showToast({
      type: 'success',
      title: 'Application Selected',
      description: `Selected ${application.name} for ENS transfer operations`
    });
  };

  // Navigation helpers
  const goToApplicationSelection = () => {
    setCurrentStep('select-app');
    setSelectedApplication(null);
    setSelectedENSName('');
    setEnsOwner('');
    setTransferError(null);
  };

  const goToENSSelection = () => {
    setCurrentStep('select-ens');
    setSelectedENSName('');
    setEnsOwner('');
    setTransferError(null);
  };

  // Handle ENS name selection and validation
  const handleENSNameSelect = (ensName: string, owner: string) => {
    setSelectedENSName(ensName);
    setEnsOwner(owner);
    setCurrentStep('transfer');
    setTransferError(null);

    showToast({
      type: 'success',
      title: 'ENS Name Selected',
      description: `Selected ${ensName} for transfer operations`
    });
  };

  // Don't render until mounted (prevents SSR issues with RainbowKit)
  if (!isMounted) {
    return (
      <Card className={`bg-white/80 backdrop-blur-sm border border-[#B497D6]/20 shadow-lg rounded-2xl hover:shadow-xl transition-all duration-300 ${className}`}>
        <CardHeader className="pb-4">
          <CardTitle className="text-lg font-bold bg-gradient-to-r from-[#4A148C] to-[#7B1FA2] bg-clip-text text-transparent flex items-center gap-2">
            <ArrowUpDownIcon className="h-5 w-5 text-[#4A148C]" />
            ENS Transfer Functions
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center p-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#4A148C]"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  // Validate Ethereum address
  const validateAddress = (address: string): boolean => {
    if (!address) {
      setAddressError('Address is required');
      return false;
    }

    if (!ethers.isAddress(address)) {
      setAddressError('Invalid Ethereum address format');
      return false;
    }

    if (address.toLowerCase() === ensOwner.toLowerCase()) {
      setAddressError('New owner cannot be the same as current owner');
      return false;
    }

    setAddressError(null);
    return true;
  };

  // Handle address input change
  const handleAddressChange = (value: string) => {
    setNewOwnerAddress(value);
    if (value) {
      validateAddress(value);
    } else {
      setAddressError(null);
    }
  };



  // Handle wallet disconnect
  const handleDisconnectWallet = () => {
    disconnect();
    // Reset form state
    setSelectedENSName('');
    setEnsOwner('');
    setNewOwnerAddress('');
    setTransferStatus('idle');
    setTransferError(null);
    setTxHash(null);
    setAddressError(null);

    showToast({
      type: 'info',
      title: 'Wallet Disconnected',
      description: 'Your wallet has been disconnected successfully'
    });
  };

  // Handle wallet reconnection
  const handleReconnectWallet = () => {
    disconnect();

    showToast({
      type: 'info',
      title: 'Wallet Disconnected',
      description: 'Please connect your wallet again using the Connect Wallet button'
    });
  };



  // Handle registrar creation
  const handleCreateRegistrar = async () => {
    if (!isConnected || !address || !token || !selectedENSName) {
      setTransferError('Please connect your wallet, ensure you are logged in, and enter an ENS name');
      return;
    }

    if (!selectedApplication && !initialAppId) {
      setTransferError('Please select an application first');
      return;
    }

    if (!isValidChain(selectedChain)) {
      setTransferError('Invalid chain selected');
      return;
    }

    // Use selected application's appId or fallback to initialAppId
    const appIdToUse = selectedApplication?.appId || initialAppId;

    if (!appIdToUse) {
      setTransferError('Application ID is required for ENS registrar operations. Please select an application.');
      return;
    }

    setTransferStatus('preparing');
    setTransferError(null);

    try {
      // Use backend API to prepare the transaction
      const response = await apiService.prepareRegistrarTransaction(
        {
          ensName: selectedENSName,
          chain: selectedChain
        },
        token,
        appIdToUse
      );

      if (!response.success || !response.data?.data) {
        throw new Error(response.error || 'Failed to prepare registrar transaction');
      }

      const transactionData = response.data.data;
      console.log('Backend transaction data:', transactionData);

      // Use the backend-prepared transaction data with wagmi
      writeFactoryContract({
        address: transactionData.to as `0x${string}`,
        abi: FACTORY_CONTRACT_ABI,
        functionName: 'createSubnameRegistrar',
        args: [namehash(selectedENSName.toLowerCase()) as `0x${string}`],
        chain: undefined,
        account: undefined,
      } as any);

      stableShowToast({
        type: 'info',
        title: 'Transaction Initiated',
        description: 'Please confirm the registrar creation in your wallet'
      });

    } catch (error: any) {
      console.error('Registrar creation error:', error);
      setTransferStatus('error');

      let errorMessage = 'Failed to create registrar';

      if (error.code === 'ACTION_REJECTED') {
        errorMessage = 'Transaction was rejected by user';
      } else if (error.message?.includes('insufficient funds')) {
        errorMessage = 'Insufficient funds for gas fees';
      } else if (error.message?.includes('Application ID is required')) {
        errorMessage = 'Please select an application before proceeding with ENS operations.';
      } else if (error.message) {
        errorMessage = error.message;
      }

      setTransferError(errorMessage);
      onError?.(errorMessage);

      stableShowToast({
        type: 'error',
        title: 'Registrar Creation Failed',
        description: errorMessage
      });
    }
  };

  // Handle NameWrapper transfer
  const handleNameWrapperTransfer = async () => {
    if (!isConnected || !address || !token || !selectedENSName) {
      setTransferError('Please connect your wallet, ensure you are logged in, and enter an ENS name');
      return;
    }

    if (!selectedApplication && !initialAppId) {
      setTransferError('Please select an application first');
      return;
    }

    if (!validateAddress(newOwnerAddress)) {
      return;
    }

    if (!isValidChain(selectedChain)) {
      setTransferError('Invalid chain selected');
      return;
    }

    // Check if current user is the owner
    if (address.toLowerCase() !== ensOwner.toLowerCase()) {
      setTransferError('Only the current owner can transfer ownership');
      return;
    }

    // Use selected application's appId or fallback to initialAppId
    const appIdToUse = selectedApplication?.appId || initialAppId;

    if (!appIdToUse) {
      setTransferError('Application ID is required for ENS NameWrapper operations. Please select an application.');
      return;
    }

    setTransferStatus('preparing');
    setTransferError(null);

    try {
      // Validate NameWrapper transfer before proceeding
      stableShowToast({
        type: 'info',
        title: 'Validating Transfer',
        description: 'Checking if ENS name is wrapped and validating ownership...'
      });

      const validation = await validateNameWrapperTransfer(
        selectedENSName,
        ensOwner,
        newOwnerAddress,
        selectedChain
      );

      if (!validation.isValid) {
        throw new Error(validation.error || 'NameWrapper transfer validation failed');
      }

      const tokenId = getENSTokenId(selectedENSName);

      // Get the correct NameWrapper contract address for the selected chain
      const nameWrapperAddress = getNameWrapperAddress(selectedChain);

      // Use backend API to prepare the NameWrapper transfer
      const response = await apiService.prepareNameWrapperTransfer(
        {
          chain: selectedChain,
          from: ensOwner,
          to: newOwnerAddress,
          id: tokenId,
          amount: "1",
          data: "0x"
        },
        token,
        appIdToUse
      );

      if (!response.success || !response.data?.data) {
        throw new Error(response.error || 'Failed to prepare NameWrapper transfer');
      }

      const transactionData = response.data.data;
      console.log('Backend NameWrapper transaction data:', transactionData);

      // Ensure we're using the correct NameWrapper contract address
      const contractAddress = transactionData.to.toLowerCase() === nameWrapperAddress.toLowerCase()
        ? transactionData.to
        : nameWrapperAddress;

      console.log('Using NameWrapper contract address:', contractAddress);

      // Use the backend-prepared transaction data with wagmi
      writeNameWrapperContract({
        address: contractAddress as `0x${string}`,
        abi: NAMEWRAPPER_CONTRACT_ABI,
        functionName: 'safeTransferFrom',
        args: [
          ensOwner as `0x${string}`,
          newOwnerAddress as `0x${string}`,
          BigInt(tokenId),
          BigInt(1),
          '0x' as `0x${string}`
        ],
        chain: undefined,
        account: undefined,
      } as any);

      stableShowToast({
        type: 'info',
        title: 'Transfer Initiated',
        description: 'Please confirm the NameWrapper transfer in your wallet'
      });

    } catch (error: any) {
      console.error('NameWrapper transfer error:', error);
      setTransferStatus('error');

      let errorMessage = 'Failed to transfer via NameWrapper';

      if (error.code === 'ACTION_REJECTED') {
        errorMessage = 'Transaction was rejected by user';
      } else if (error.message?.includes('insufficient funds')) {
        errorMessage = 'Insufficient funds for gas fees';
      } else if (error.message?.includes('not wrapped')) {
        errorMessage = error.message + ' You may need to wrap the ENS name first before transferring it via NameWrapper.';
      } else if (error.message?.includes('not the owner')) {
        errorMessage = error.message + ' Please ensure you are the current owner of this ENS name.';
      } else if (error.message?.includes('Application ID is required')) {
        errorMessage = 'Please select an application before proceeding with ENS operations.';
      } else if (error.message) {
        errorMessage = error.message;
      }

      setTransferError(errorMessage);
      onError?.(errorMessage);

      stableShowToast({
        type: 'error',
        title: 'NameWrapper Transfer Failed',
        description: errorMessage
      });
    }
  };

  // Form validation
  const appIdToUse = selectedApplication?.appId || initialAppId;
  const isFormValid = isConnected && token && selectedENSName && appIdToUse;
  const isRegistrarFormValid = isFormValid && selectedChain;
  const isNameWrapperFormValid = isFormValid && newOwnerAddress && !addressError && selectedChain;
  const isTransferring = ['preparing', 'confirming', 'pending'].includes(transferStatus);



  return (
    <Card className={`bg-white/80 backdrop-blur-sm border border-[#B497D6]/20 shadow-lg rounded-2xl hover:shadow-xl transition-all duration-300 ${className}`}>
      <CardHeader className="pb-4">
        <CardTitle className="text-lg font-bold bg-gradient-to-r from-[#4A148C] to-[#7B1FA2] bg-clip-text text-transparent flex items-center gap-2">
          <ArrowUpDownIcon className="h-5 w-5 text-[#4A148C]" />
          ENS Transfer Functions
        </CardTitle>
        <p className="text-sm text-gray-600 mt-2">
          Advanced ENS transfer operations.
        </p>

        {/* Workflow Progress Indicator */}
        <div className="flex items-center gap-2 mt-4 flex-wrap">
          {/* Step 1: Wallet Connection */}
          <div className={`flex items-center gap-2 px-3 py-1 rounded-full text-xs font-medium ${
            isConnected ? 'bg-green-100 text-green-800' : 'bg-[#4A148C] text-white'
          }`}>
            <WalletIcon className="h-3 w-3" />
            {isConnected ? 'Wallet Connected' : 'Connect Wallet'}
          </div>

          {isConnected && (
            <>
              <div className="w-2 h-0.5 bg-green-400"></div>

              {/* Step 2: Application Selection */}
              <div className={`flex items-center gap-2 px-3 py-1 rounded-full text-xs font-medium ${
                currentStep === 'select-app' ? 'bg-[#4A148C] text-white' :
                selectedApplication || initialAppId ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-600'
              }`}>
                <GlobeIcon className="h-3 w-3" />
                {selectedApplication ? selectedApplication.name : initialAppId ? 'App Selected' : 'Select App'}
              </div>

              {(selectedApplication || initialAppId) && (
                <>
                  <div className="w-2 h-0.5 bg-green-400"></div>

                  {/* Step 3: ENS Selection */}
                  <div className={`flex items-center gap-2 px-3 py-1 rounded-full text-xs font-medium ${
                    currentStep === 'select-ens' ? 'bg-[#4A148C] text-white' :
                    selectedENSName ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-600'
                  }`}>
                    <UserIcon className="h-3 w-3" />
                    {selectedENSName || 'Select ENS'}
                  </div>

                  {selectedENSName && (
                    <>
                      <div className="w-2 h-0.5 bg-green-400"></div>

                      {/* Step 4: Transfer Operations */}
                      <div className={`flex items-center gap-2 px-3 py-1 rounded-full text-xs font-medium ${
                        currentStep === 'transfer' ? 'bg-[#4A148C] text-white' : 'bg-gray-100 text-gray-600'
                      }`}>
                        <ArrowUpDownIcon className="h-3 w-3" />
                        Transfer
                      </div>
                    </>
                  )}
                </>
              )}
            </>
          )}
        </div>
      </CardHeader>

      <CardContent className="space-y-6">
        {/* Wallet Connection */}
        {!isConnected ? (
          <div className="p-6 bg-gradient-to-br from-[#4A148C]/5 via-[#7B1FA2]/5 to-[#4A148C]/10 border border-[#B497D6]/30 rounded-2xl text-center">
            <div className="space-y-4">
              <div className="w-16 h-16 mx-auto bg-gradient-to-r from-[#4A148C] to-[#7B1FA2] rounded-full flex items-center justify-center shadow-lg">
                <WalletIcon className="h-8 w-8 text-white" />
              </div>
              <div className="space-y-2">
                <h3 className="text-lg font-bold bg-gradient-to-r from-[#4A148C] to-[#7B1FA2] bg-clip-text text-transparent">
                  Connect Your Wallet
                </h3>
                <p className="text-sm text-gray-600 max-w-md mx-auto">
                  Connect your wallet to access ENS transfer functions. This allows you to create registrar contracts and transfer ENS names.
                </p>
              </div>
              <div className="flex justify-center">
                <ConnectButton />
              </div>
            </div>
          </div>
        ) : (
          <>
            {/* Step 1: Application Selection */}
            {currentStep === 'select-app' && !initialAppId && (
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <h3 className="text-lg font-semibold text-[#4A148C]">Step 2: Select Application</h3>
                </div>

                {/* Wallet Status */}
                <div className="p-4 bg-gradient-to-r from-green-50 to-emerald-50 border border-green-200 rounded-lg">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <div className="w-10 h-10 bg-gradient-to-r from-green-500 to-emerald-500 rounded-full flex items-center justify-center">
                        <CheckCircleIcon className="h-5 w-5 text-white" />
                      </div>
                      <div>
                        <p className="text-sm font-medium text-green-800">Wallet Connected</p>
                        <code className="text-xs text-green-700 bg-green-100 px-2 py-1 rounded">
                          {address}
                        </code>
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      <Button
                        onClick={handleReconnectWallet}
                        size="sm"
                        variant="outline"
                        className="border-green-300 text-green-700 hover:bg-green-100"
                        disabled={isTransferring}
                      >
                        <RefreshCwIcon className="h-3 w-3 mr-1" />
                        Switch
                      </Button>
                      <Button
                        onClick={handleDisconnectWallet}
                        size="sm"
                        variant="outline"
                        className="border-red-300 text-red-700 hover:bg-red-100"
                        disabled={isTransferring}
                      >
                        <LogOutIcon className="h-3 w-3 mr-1" />
                        Disconnect
                      </Button>
                    </div>
                  </div>
                </div>

                <ApplicationSelection
                  selectedApplicationId={selectedApplication?.appId}
                  onApplicationSelect={handleApplicationSelect}
                  onError={(error) => {
                    setTransferError(error);
                    onError?.(error);
                  }}
                  className="mb-6"
                />

                {/* Next Button */}
                {selectedApplication && (
                  <div className="flex justify-end">
                    <Button
                      onClick={() => setCurrentStep('select-ens')}
                      className="bg-gradient-to-r from-[#4A148C] to-[#7B1FA2] hover:from-[#5A1A9C] hover:to-[#8B2FAC] text-white shadow-lg hover:shadow-xl transform hover:scale-105"
                    >
                      <ArrowRightIcon className="w-4 h-4 mr-2" />
                      Next: Select ENS Name
                    </Button>
                  </div>
                )}
              </div>
            )}

            {/* Step 3: ENS Name Selection */}
            {currentStep === 'select-ens' && (selectedApplication || initialAppId) && (
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <h3 className="text-lg font-semibold text-[#4A148C]">Step 3: Select ENS Name</h3>
                  {!initialAppId && (
                    <Button
                      onClick={goToApplicationSelection}
                      variant="outline"
                      size="sm"
                      className="border-[#7B1FA2]/30 text-[#4A148C] hover:bg-[#7B1FA2]/5"
                    >
                      Change App
                    </Button>
                  )}
                </div>

                {/* Show selected application info */}
                {selectedApplication && (
                  <div className="p-3 bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-lg">
                    <div className="flex items-center gap-2">
                      <GlobeIcon className="h-4 w-4 text-blue-600" />
                      <span className="text-sm font-medium text-blue-800">
                        Selected Application: {selectedApplication.name}
                      </span>
                    </div>
                  </div>
                )}

                {/* ENS Name Input */}
                <div className="space-y-3">
                  <Label htmlFor="ensName" className="text-sm font-medium text-[#4A148C]">
                    ENS Name
                  </Label>
                  <div className="flex gap-2">
                    <Input
                      id="ensName"
                      type="text"
                      placeholder="example.eth"
                      value={selectedENSName}
                      onChange={(e) => setSelectedENSName(e.target.value)}
                      className="border-[#7B1FA2]/30 focus:border-[#4A148C] focus:ring-[#4A148C]/20"
                      disabled={isTransferring}
                      onKeyDown={(e) => {
                        if (e.key === 'Enter' && selectedENSName) {
                          handleENSNameSelect(selectedENSName, address || '');
                        }
                      }}
                    />
                  </div>
                </div>

                {/* Next Button */}
                {selectedENSName && (
                  <div className="flex justify-end">
                    <Button
                      onClick={() => handleENSNameSelect(selectedENSName, address || '')}
                      disabled={!selectedENSName || isTransferring}
                      className="bg-gradient-to-r from-[#4A148C] to-[#7B1FA2] hover:from-[#5A1A9C] hover:to-[#8B2FAC] text-white shadow-lg hover:shadow-xl transform hover:scale-105"
                    >
                      <ArrowRightIcon className="w-4 h-4 mr-2" />
                      Next: Transfer Operations
                    </Button>
                  </div>
                )}
              </div>
            )}

            {/* Step 4: Transfer Operations */}
            {currentStep === 'transfer' && selectedENSName && (selectedApplication || initialAppId) && (
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <h3 className="text-lg font-semibold text-[#4A148C]">Step 4: Transfer Operations</h3>
                  <Button
                    onClick={goToENSSelection}
                    variant="outline"
                    size="sm"
                    className="border-[#7B1FA2]/30 text-[#4A148C] hover:bg-[#7B1FA2]/5"
                  >
                    Change ENS
                  </Button>
                </div>

                {/* Selected ENS Info */}
                <div className="p-4 bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-lg">
                  <div className="flex items-center gap-3">
                    <UserIcon className="h-5 w-5 text-blue-600" />
                    <div>
                      <p className="text-sm font-medium text-blue-800">Selected ENS: {selectedENSName}</p>
                      <code className="text-xs text-blue-700 bg-blue-100 px-2 py-1 rounded">
                        Owner: {ensOwner || address}
                      </code>
                    </div>
                  </div>
                </div>

                {/* Chain Selection */}
                <div className="space-y-2">
                  <Label htmlFor="chain" className="text-sm font-medium text-[#4A148C]">
                    Blockchain Network
                  </Label>
                  <Select
                    value={selectedChain}
                    onChange={(e) => setSelectedChain(e.target.value)}
                    disabled={isTransferring}
                    className="border-[#7B1FA2]/30 focus:border-[#4A148C] focus:ring-[#4A148C]/20"
                  >
                    <SelectOption value="sepolia">Sepolia Testnet</SelectOption>
                    <SelectOption value="mainnet">Ethereum Mainnet</SelectOption>
                    <SelectOption value="goerli">Goerli Testnet</SelectOption>
                  </Select>
                </div>

                {/* Transfer Type Selection */}
                <div className="space-y-2">
                  <Label className="text-sm font-medium text-[#4A148C]">
                    Transfer Operation
                  </Label>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                    <Button
                      onClick={() => setTransferType('registrar')}
                      variant={transferType === 'registrar' ? 'default' : 'outline'}
                      className={`h-auto p-4 text-left ${
                        transferType === 'registrar'
                          ? 'bg-gradient-to-r from-[#4A148C] to-[#7B1FA2] text-white'
                          : 'border-[#7B1FA2]/30 hover:bg-[#7B1FA2]/5'
                      }`}
                      disabled={isTransferring}
                    >
                      <div className="space-y-1">
                        <div className="flex items-center gap-2">
                          <SettingsIcon className="h-4 w-4" />
                          <span className="font-medium">Create Registrar</span>
                        </div>
                        <p className="text-xs opacity-80">
                          Create a subname registrar contract
                        </p>
                      </div>
                    </Button>

                    <Button
                      onClick={() => setTransferType('namewrapper')}
                      variant={transferType === 'namewrapper' ? 'default' : 'outline'}
                      className={`h-auto p-4 text-left ${
                        transferType === 'namewrapper'
                          ? 'bg-gradient-to-r from-[#4A148C] to-[#7B1FA2] text-white'
                          : 'border-[#7B1FA2]/30 hover:bg-[#7B1FA2]/5'
                      }`}
                      disabled={isTransferring}
                    >
                      <div className="space-y-1">
                        <div className="flex items-center gap-2">
                          <ArrowRightIcon className="h-4 w-4" />
                          <span className="font-medium">NameWrapper Transfer</span>
                        </div>
                        <p className="text-xs opacity-80">
                          Transfer wrapped ENS name
                        </p>
                      </div>
                    </Button>
                  </div>
                </div>

                {/* NameWrapper Transfer - New Owner Input */}
                {transferType === 'namewrapper' && (
                  <div className="space-y-2">
                    <Label htmlFor="newOwner" className="text-sm font-medium text-[#4A148C]">
                      New Owner Address
                    </Label>
                    <Input
                      id="newOwner"
                      type="text"
                      placeholder="0x..."
                      value={newOwnerAddress}
                      onChange={(e) => handleAddressChange(e.target.value)}
                      className={`border-[#7B1FA2]/30 focus:border-[#4A148C] focus:ring-[#4A148C]/20 ${
                        addressError ? 'border-red-500' : ''
                      }`}
                      disabled={isTransferring}
                    />
                    {addressError && (
                      <p className="text-sm text-red-600 flex items-center gap-1">
                        <AlertCircleIcon className="h-4 w-4" />
                        {addressError}
                      </p>
                    )}
                  </div>
                )}

                {/* Transfer Error */}
                {transferError && (
                  <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
                    <div className="flex items-start gap-3">
                      <AlertCircleIcon className="h-5 w-5 text-red-600 mt-0.5 flex-shrink-0" />
                      <div className="text-sm">
                        <p className="text-red-800 font-medium">Operation Failed</p>
                        <p className="text-red-700">{transferError}</p>
                      </div>
                    </div>
                  </div>
                )}

                {/* Success Message */}
                {transferStatus === 'success' && txHash && (
                  <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
                    <div className="flex items-start gap-3">
                      <CheckCircleIcon className="h-5 w-5 text-green-600 mt-0.5 flex-shrink-0" />
                      <div className="text-sm flex-1">
                        <p className="text-green-800 font-medium mb-1">Operation Successful!</p>
                        <p className="text-green-700 mb-2">
                          {transferType === 'registrar'
                            ? `Registrar contract for ${selectedENSName} has been created successfully.`
                            : `${selectedENSName} has been transferred via NameWrapper successfully.`
                          }
                        </p>
                        <Button
                          onClick={() => {
                            const explorerUrl = selectedChain === 'mainnet'
                              ? `https://etherscan.io/tx/${txHash}`
                              : selectedChain === 'sepolia'
                              ? `https://sepolia.etherscan.io/tx/${txHash}`
                              : `https://goerli.etherscan.io/tx/${txHash}`;
                            window.open(explorerUrl, '_blank');
                          }}
                          size="sm"
                          variant="outline"
                          className="border-green-300 hover:bg-green-100 text-green-700"
                        >
                          <ExternalLinkIcon className="mr-1 h-3 w-3" />
                          View on {selectedChain === 'mainnet' ? 'Etherscan' : selectedChain === 'sepolia' ? 'Sepolia Etherscan' : 'Goerli Etherscan'}
                        </Button>
                      </div>
                    </div>
                  </div>
                )}

                {/* Action Button */}
                <Button
                  onClick={transferType === 'registrar' ? handleCreateRegistrar : handleNameWrapperTransfer}
                  disabled={
                    transferType === 'registrar'
                      ? !isRegistrarFormValid || isTransferring || transferStatus === 'success'
                      : !isNameWrapperFormValid || isTransferring || transferStatus === 'success'
                  }
                  className={`w-full transition-all duration-300 ${
                    (transferType === 'registrar' ? !isRegistrarFormValid : !isNameWrapperFormValid) ||
                    isTransferring ||
                    transferStatus === 'success'
                      ? 'opacity-50 cursor-not-allowed'
                      : 'bg-gradient-to-r from-[#4A148C] to-[#7B1FA2] hover:from-[#5A1A9C] hover:to-[#8B2FAC] text-white shadow-lg hover:shadow-xl transform hover:scale-105'
                  }`}
                >
                  {transferStatus === 'preparing' ? (
                    <>
                      <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"></div>
                      Preparing Transaction...
                    </>
                  ) : transferStatus === 'confirming' ? (
                    <>
                      <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"></div>
                      Confirm in Wallet...
                    </>
                  ) : transferStatus === 'pending' ? (
                    <>
                      <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"></div>
                      Transaction Pending...
                    </>
                  ) : transferStatus === 'success' ? (
                    <>
                      <CheckCircleIcon className="w-4 h-4 mr-2" />
                      Operation Complete
                    </>
                  ) : (
                    <>
                      {transferType === 'registrar' ? (
                        <>
                          <SettingsIcon className="w-4 h-4 mr-2" />
                          Create Registrar Contract
                        </>
                      ) : (
                        <>
                          <ShieldCheckIcon className="w-4 h-4 mr-2" />
                          Transfer via NameWrapper
                        </>
                      )}
                    </>
                  )}
                </Button>

                {/* Transaction Hash */}
                {txHash && transferStatus === 'pending' && (
                  <div className="text-center">
                    <p className="text-sm text-gray-600 mb-2">Transaction Hash:</p>
                    <code className="text-xs bg-gray-100 px-2 py-1 rounded break-all">
                      {txHash}
                    </code>
                  </div>
                )}
              </div>
            )}
          </>
        )}
      </CardContent>
    </Card>
  );
}
