'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { useAccount, useWriteContract, useWaitForTransactionReceipt } from 'wagmi';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { useToast } from '@/lib/toast-context';
import { useAuth } from '@/lib/auth-context';
import { apiService } from '@/lib/api';
import { ApplicationWithApiKey } from '@/lib/api';
import { 
  CheckCircleIcon, 
  ArrowRightIcon, 
  WalletIcon, 
  GlobeIcon,
  ArrowUpDownIcon,
  LoaderIcon,
  AlertCircleIcon
} from 'lucide-react';
import { getENSTokenId } from '@/lib/ens-utils';

interface ENSWorkflowProps {
  selectedApplication: ApplicationWithApiKey;
  onSuccess?: (data: any) => void;
  className?: string;
}

type WorkflowStep = 'register' | 'transfer' | 'store' | 'complete';

interface ENSWorkflowState {
  currentStep: WorkflowStep;
  ensName: string;
  contractAddress: string;
  chain: string;
  registrarTxHash?: string;
  transferTxHash?: string;
  isLoading: boolean;
  error?: string;
}

export function ENSWorkflow({ selectedApplication, onSuccess, className = "" }: ENSWorkflowProps) {
  const { address, isConnected } = useAccount();
  const { showToast } = useToast();
  const { token } = useAuth();
  
  const [state, setState] = useState<ENSWorkflowState>({
    currentStep: 'register',
    ensName: '',
    contractAddress: address || '',
    chain: 'sepolia',
    isLoading: false
  });

  // Contract write hooks
  const { writeContract: writeRegistrar, data: registrarTxHash } = useWriteContract();
  const { writeContract: writeTransfer, data: transferTxHash } = useWriteContract();
  
  // Transaction receipt hooks
  const { isLoading: isRegistrarPending, isSuccess: isRegistrarSuccess } = useWaitForTransactionReceipt({
    hash: registrarTxHash,
  });
  
  const { isLoading: isTransferPending, isSuccess: isTransferSuccess } = useWaitForTransactionReceipt({
    hash: transferTxHash,
  });

  // Update contract address when wallet changes
  useEffect(() => {
    if (address && !state.contractAddress) {
      setState(prev => ({ ...prev, contractAddress: address }));
    }
  }, [address, state.contractAddress]);

  const handleStoreENS = useCallback(async () => {
    if (!token || !state.ensName) return;

    setState(prev => ({ ...prev, isLoading: true, error: undefined }));

    try {
      // Step 3: Store ENS registration in backend
      const response = await apiService.registerENSRoot(
        {
          ens_name: state.ensName,
          contractAddress: state.contractAddress,
          chain: state.chain,
          isActive: true
        },
        token,
        selectedApplication.appId
      );

      if (!response.success) {
        throw new Error(response.error || 'Failed to store ENS registration');
      }

      setState(prev => ({
        ...prev,
        currentStep: 'complete',
        isLoading: false
      }));

      showToast({
        type: 'success',
        title: 'ENS Registration Complete! 🎉',
        description: `${state.ensName} has been successfully registered and linked to ${selectedApplication.name}`
      });

      onSuccess?.(response.data);

    } catch (error) {
      console.error('Store ENS failed:', error);
      setState(prev => ({
        ...prev,
        isLoading: false,
        error: error instanceof Error ? error.message : 'Failed to store ENS registration'
      }));
      showToast({
        type: 'error',
        title: 'Storage Failed',
        description: error instanceof Error ? error.message : 'Unknown error occurred'
      });
    }
  }, [token, state.ensName, state.contractAddress, state.chain, selectedApplication.appId, selectedApplication.name, showToast, onSuccess]);

  // Handle registrar transaction success
  useEffect(() => {
    if (isRegistrarSuccess && registrarTxHash) {
      setState(prev => ({
        ...prev,
        registrarTxHash: registrarTxHash,
        currentStep: 'transfer',
        isLoading: false
      }));
      showToast({
        type: 'success',
        title: 'Registrar Created',
        description: 'Subname registrar contract created successfully'
      });
    }
  }, [isRegistrarSuccess, registrarTxHash, showToast]);

  // Handle transfer transaction success
  useEffect(() => {
    if (isTransferSuccess && transferTxHash) {
      setState(prev => ({
        ...prev,
        transferTxHash: transferTxHash,
        currentStep: 'store',
        isLoading: false
      }));
      showToast({
        type: 'success',
        title: 'Transfer Complete',
        description: 'ENS name transferred to contract successfully'
      });
      // Automatically proceed to store step
      handleStoreENS();
    }
  }, [isTransferSuccess, transferTxHash, showToast, handleStoreENS]);

  const handleCreateRegistrar = async () => {
    if (!token || !state.ensName || !isConnected) return;

    setState(prev => ({ ...prev, isLoading: true, error: undefined }));

    try {
      // Step 1: Prepare registrar transaction
      const response = await apiService.prepareRegistrarTransaction(
        {
          ensName: state.ensName,
          chain: state.chain,
          from: address!
        },
        token,
        selectedApplication.appId
      );

      if (!response.success || !response.data?.data) {
        throw new Error(response.error || 'Failed to prepare registrar transaction');
      }

      const txData = response.data.data;
      
      // Execute transaction using wagmi
      writeRegistrar({
        to: txData.to as `0x${string}`,
        data: txData.data as `0x${string}`,
        value: BigInt(txData.value || '0'),
      });

    } catch (error) {
      console.error('Create registrar failed:', error);
      setState(prev => ({ 
        ...prev, 
        isLoading: false, 
        error: error instanceof Error ? error.message : 'Failed to create registrar' 
      }));
      showToast({
        type: 'error',
        title: 'Registrar Creation Failed',
        description: error instanceof Error ? error.message : 'Unknown error occurred'
      });
    }
  };

  const handleTransferENS = async () => {
    if (!token || !state.ensName || !isConnected) return;

    setState(prev => ({ ...prev, isLoading: true, error: undefined }));

    try {
      const tokenId = getENSTokenId(state.ensName);
      
      // Step 2: Prepare NameWrapper transfer
      const response = await apiService.prepareNameWrapperTransfer(
        {
          chain: state.chain,
          from: address!,
          to: state.contractAddress,
          id: tokenId,
          amount: "1",
          data: "0x"
        },
        token,
        selectedApplication.appId
      );

      if (!response.success || !response.data?.data) {
        throw new Error(response.error || 'Failed to prepare transfer transaction');
      }

      const txData = response.data.data;
      
      // Execute transfer using wagmi
      writeTransfer({
        to: txData.to as `0x${string}`,
        data: txData.data as `0x${string}`,
        value: BigInt(txData.value || '0'),
      });

    } catch (error) {
      console.error('Transfer ENS failed:', error);
      setState(prev => ({ 
        ...prev, 
        isLoading: false, 
        error: error instanceof Error ? error.message : 'Failed to transfer ENS' 
      }));
      showToast({
        type: 'error',
        title: 'Transfer Failed',
        description: error instanceof Error ? error.message : 'Unknown error occurred'
      });
    }
  };



  const getStepStatus = (step: WorkflowStep) => {
    const stepOrder: WorkflowStep[] = ['register', 'transfer', 'store', 'complete'];
    const currentIndex = stepOrder.indexOf(state.currentStep);
    const stepIndex = stepOrder.indexOf(step);
    
    if (stepIndex < currentIndex) return 'completed';
    if (stepIndex === currentIndex) return 'current';
    return 'pending';
  };

  const isStepLoading = (step: WorkflowStep) => {
    return state.currentStep === step && (
      state.isLoading || 
      (step === 'register' && isRegistrarPending) ||
      (step === 'transfer' && isTransferPending)
    );
  };

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Workflow Progress */}
      <Card className="bg-white/80 backdrop-blur-sm border border-[#B497D6]/20 shadow-lg rounded-2xl">
        <CardHeader>
          <CardTitle className="text-lg font-bold bg-gradient-to-r from-[#4A148C] to-[#7B1FA2] bg-clip-text text-transparent">
            ENS Integration Workflow
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-between mb-6">
            {[
              { step: 'register', label: 'Create Registrar', icon: GlobeIcon },
              { step: 'transfer', label: 'Transfer ENS', icon: ArrowUpDownIcon },
              { step: 'store', label: 'Store Registration', icon: WalletIcon },
              { step: 'complete', label: 'Complete', icon: CheckCircleIcon }
            ].map(({ step, label, icon: Icon }, index) => {
              const status = getStepStatus(step as WorkflowStep);
              const isLoading = isStepLoading(step as WorkflowStep);
              
              return (
                <div key={step} className="flex items-center">
                  <div className={`
                    flex items-center justify-center w-10 h-10 rounded-full border-2 transition-all
                    ${status === 'completed' ? 'bg-green-500 border-green-500 text-white' : ''}
                    ${status === 'current' ? 'bg-[#4A148C] border-[#4A148C] text-white' : ''}
                    ${status === 'pending' ? 'bg-gray-100 border-gray-300 text-gray-400' : ''}
                  `}>
                    {isLoading ? (
                      <LoaderIcon className="h-5 w-5 animate-spin" />
                    ) : (
                      <Icon className="h-5 w-5" />
                    )}
                  </div>
                  <div className="ml-3">
                    <p className={`text-sm font-medium ${
                      status === 'completed' ? 'text-green-600' :
                      status === 'current' ? 'text-[#4A148C]' : 'text-gray-400'
                    }`}>
                      {label}
                    </p>
                  </div>
                  {index < 3 && (
                    <ArrowRightIcon className="h-4 w-4 text-gray-300 mx-4" />
                  )}
                </div>
              );
            })}
          </div>
        </CardContent>
      </Card>

      {/* Step Content */}
      {state.currentStep === 'register' && (
        <Card className="bg-white/80 backdrop-blur-sm border border-[#B497D6]/20 shadow-lg rounded-2xl">
          <CardHeader>
            <CardTitle className="text-lg font-bold text-[#4A148C] flex items-center gap-2">
              <GlobeIcon className="h-5 w-5" />
              Step 1: Create Subname Registrar
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label htmlFor="ensName">ENS Name</Label>
              <Input
                id="ensName"
                placeholder="myproject.eth"
                value={state.ensName}
                onChange={(e) => setState(prev => ({ ...prev, ensName: e.target.value }))}
                className="mt-1"
              />
            </div>

            <div>
              <Label htmlFor="contractAddress">Contract Address</Label>
              <Input
                id="contractAddress"
                placeholder="0x..."
                value={state.contractAddress}
                onChange={(e) => setState(prev => ({ ...prev, contractAddress: e.target.value }))}
                className="mt-1"
              />
            </div>

            <div>
              <Label htmlFor="chain">Chain</Label>
              <select
                id="chain"
                value={state.chain}
                onChange={(e) => setState(prev => ({ ...prev, chain: e.target.value }))}
                className="mt-1 w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#4A148C]"
              >
                <option value="sepolia">Sepolia</option>
                <option value="mainnet">Mainnet</option>
              </select>
            </div>

            {state.error && (
              <div className="flex items-center gap-2 p-3 bg-red-50 border border-red-200 rounded-lg">
                <AlertCircleIcon className="h-5 w-5 text-red-500" />
                <p className="text-sm text-red-700">{state.error}</p>
              </div>
            )}

            <Button
              onClick={handleCreateRegistrar}
              disabled={!state.ensName || !state.contractAddress || state.isLoading || isRegistrarPending}
              className="w-full bg-gradient-to-r from-[#4A148C] to-[#7B1FA2] hover:from-[#6A1B9A] hover:to-[#8E24AA]"
            >
              {state.isLoading || isRegistrarPending ? (
                <>
                  <LoaderIcon className="h-4 w-4 mr-2 animate-spin" />
                  Creating Registrar...
                </>
              ) : (
                'Create Subname Registrar'
              )}
            </Button>
          </CardContent>
        </Card>
      )}

      {state.currentStep === 'transfer' && (
        <Card className="bg-white/80 backdrop-blur-sm border border-[#B497D6]/20 shadow-lg rounded-2xl">
          <CardHeader>
            <CardTitle className="text-lg font-bold text-[#4A148C] flex items-center gap-2">
              <ArrowUpDownIcon className="h-5 w-5" />
              Step 2: Transfer ENS to Contract
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
              <p className="text-sm text-blue-700">
                Transfer your ENS name from your wallet to the contract address to enable subname creation.
              </p>
            </div>

            <div className="space-y-2">
              <p className="text-sm font-medium text-gray-700">ENS Name: {state.ensName}</p>
              <p className="text-sm font-medium text-gray-700">From: {address}</p>
              <p className="text-sm font-medium text-gray-700">To: {state.contractAddress}</p>
            </div>

            {state.error && (
              <div className="flex items-center gap-2 p-3 bg-red-50 border border-red-200 rounded-lg">
                <AlertCircleIcon className="h-5 w-5 text-red-500" />
                <p className="text-sm text-red-700">{state.error}</p>
              </div>
            )}

            <Button
              onClick={handleTransferENS}
              disabled={state.isLoading || isTransferPending}
              className="w-full bg-gradient-to-r from-[#4A148C] to-[#7B1FA2] hover:from-[#6A1B9A] hover:to-[#8E24AA]"
            >
              {state.isLoading || isTransferPending ? (
                <>
                  <LoaderIcon className="h-4 w-4 mr-2 animate-spin" />
                  Transferring ENS...
                </>
              ) : (
                'Transfer ENS Name'
              )}
            </Button>
          </CardContent>
        </Card>
      )}

      {state.currentStep === 'store' && (
        <Card className="bg-white/80 backdrop-blur-sm border border-[#B497D6]/20 shadow-lg rounded-2xl">
          <CardHeader>
            <CardTitle className="text-lg font-bold text-[#4A148C] flex items-center gap-2">
              <WalletIcon className="h-5 w-5" />
              Step 3: Store Registration
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
              <p className="text-sm text-green-700">
                Storing your ENS registration in the Crefy Connect backend...
              </p>
            </div>

            {state.isLoading && (
              <div className="flex items-center justify-center py-4">
                <LoaderIcon className="h-6 w-6 animate-spin text-[#4A148C]" />
                <span className="ml-2 text-sm text-gray-600">Storing registration...</span>
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {state.currentStep === 'complete' && (
        <Card className="bg-white/80 backdrop-blur-sm border border-[#B497D6]/20 shadow-lg rounded-2xl">
          <CardHeader>
            <CardTitle className="text-lg font-bold text-green-600 flex items-center gap-2">
              <CheckCircleIcon className="h-5 w-5" />
              ENS Integration Complete! 🎉
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
              <p className="text-sm text-green-700">
                Your ENS name <strong>{state.ensName}</strong> has been successfully integrated with {selectedApplication.name}.
                Users can now claim subnames under your domain!
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
              <div>
                <p className="font-medium text-gray-700">Registrar Transaction:</p>
                <p className="text-gray-600 break-all">{state.registrarTxHash}</p>
              </div>
              <div>
                <p className="font-medium text-gray-700">Transfer Transaction:</p>
                <p className="text-gray-600 break-all">{state.transferTxHash}</p>
              </div>
            </div>

            <Button
              onClick={() => window.location.reload()}
              className="w-full bg-gradient-to-r from-[#4A148C] to-[#7B1FA2] hover:from-[#6A1B9A] hover:to-[#8E24AA]"
            >
              View ENS Dashboard
            </Button>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
